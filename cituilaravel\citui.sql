/*
 Navicat Premium Data Transfer

 Source Server         : AA本地数据库
 Source Server Type    : MySQL
 Source Server Version : 50726
 Source Host           : localhost:3306
 Source Schema         : citui

 Target Server Type    : MySQL
 Target Server Version : 50726
 File Encoding         : 65001

 Date: 18/08/2025 13:45:36
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for zc_admin_users
-- ----------------------------
DROP TABLE IF EXISTS `zc_admin_users`;
CREATE TABLE `zc_admin_users`  (
  `admin_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password_hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '真实姓名',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '邮箱地址',
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机号码',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '头像URL',
  `role` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'admin' COMMENT '角色',
  `permissions` json NULL COMMENT '权限列表',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `last_login_at` timestamp(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录IP',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`admin_id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username`) USING BTREE,
  UNIQUE INDEX `uk_email`(`email`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE,
  INDEX `idx_username`(`username`) USING BTREE,
  INDEX `idx_email`(`email`) USING BTREE,
  INDEX `idx_phone`(`phone`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_role`(`role`) USING BTREE,
  INDEX `idx_last_login_at`(`last_login_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '管理员用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_app_categories
-- ----------------------------
DROP TABLE IF EXISTS `zc_app_categories`;
CREATE TABLE `zc_app_categories`  (
  `category_id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `category_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类代码',
  `parent_id` int(11) UNSIGNED NULL DEFAULT NULL COMMENT '父分类ID',
  `category_icon` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '分类图标URL',
  `sort_order` int(11) NULL DEFAULT 0 COMMENT '排序顺序',
  `is_active` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`category_id`) USING BTREE,
  UNIQUE INDEX `uk_category_code`(`category_code`) USING BTREE,
  INDEX `idx_category_code`(`category_code`) USING BTREE,
  INDEX `idx_parent_id`(`parent_id`) USING BTREE,
  INDEX `idx_sort_order`(`sort_order`) USING BTREE,
  INDEX `idx_is_active`(`is_active`) USING BTREE,
  INDEX `idx_parent_active`(`parent_id`, `is_active`) USING BTREE,
  INDEX `idx_active_sort`(`is_active`, `sort_order`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'APP分类表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_apps
-- ----------------------------
DROP TABLE IF EXISTS `zc_apps`;
CREATE TABLE `zc_apps`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` int(11) UNSIGNED NOT NULL COMMENT '分类ID',
  `app_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名称',
  `app_package` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用包名',
  `app_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '应用版本',
  `developer` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开发商',
  `app_label` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签',
  `app_size` bigint(20) NULL DEFAULT NULL COMMENT '应用大小(字节)',
  `download_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '下载链接',
  `logo_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Logo图片URL',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '应用描述',
  `features` json NULL COMMENT '应用特性(JSON格式)',
  `screenshots` json NULL COMMENT '应用截图(JSON格式)',
  `rating` decimal(3, 2) NULL DEFAULT 0.00 COMMENT '平均评分',
  `rating_count` int(11) NULL DEFAULT 0 COMMENT '评分人数',
  `download_count` bigint(20) NULL DEFAULT 0 COMMENT '下载次数',
  `view_count` bigint(20) NULL DEFAULT 0 COMMENT '查看次数',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '应用状态',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_category_id`(`category_id`) USING BTREE,
  INDEX `idx_app_name`(`app_name`) USING BTREE,
  INDEX `idx_app_package`(`app_package`) USING BTREE,
  INDEX `idx_rating`(`rating`) USING BTREE,
  INDEX `idx_download_count`(`download_count`) USING BTREE,
  INDEX `idx_view_count`(`view_count`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_category_status`(`category_id`, `status`) USING BTREE,
  INDEX `idx_status_featured`(`status`, `is_featured`) USING BTREE,
  INDEX `idx_status_rating`(`status`, `rating`) USING BTREE,
  INDEX `idx_status_downloads`(`status`, `download_count`) USING BTREE,
  INDEX `idx_featured_rating`(`is_featured`, `rating`) USING BTREE,
  FULLTEXT INDEX `ft_app_name_desc`(`app_name`, `description`)
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'APP信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_evaluation_reports
-- ----------------------------
DROP TABLE IF EXISTS `zc_evaluation_reports`;
CREATE TABLE `zc_evaluation_reports`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '报告ID',
  `app_id` bigint(20) UNSIGNED NOT NULL COMMENT '应用ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `report_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告标题',
  `report_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '报告内容',
  `download_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '下载地址',
  `pingfen` smallint(3) UNSIGNED NULL DEFAULT 1 COMMENT '评分(1-5)',
  `yunxingmoshi` tinyint(3) UNSIGNED NULL DEFAULT 2 COMMENT '运行模式 1自动  2手动',
  `xinrenfuli` decimal(10, 2) UNSIGNED NULL DEFAULT NULL COMMENT '新人福利',
  `tixianmenkan` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '提现门槛',
  `dingbaojine` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '顶包金额',
  `ceshitiaoshu` smallint(5) UNSIGNED NULL DEFAULT 1 COMMENT '测试条数',
  `ceshishouyi` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '测试总收益',
  `ceshishichang` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '测试时长',
  `ceshishebei` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '测试设备',
  `cepingren` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '测评人',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '状态',
  `view_count` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '点赞次数',
  `is_featured` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否推荐',
  `cepingriqi` date NULL DEFAULT NULL COMMENT '测试日期',
  `shouyi_1` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第一条价格',
  `shouyi_2` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第二条价格',
  `shouyi_3` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第三条价格',
  `shouyi_4` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第四条价格',
  `shouyi_5` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '第五条价格',
  `pic_main` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '主图',
  `pic_tixian` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '提现记录截图',
  `pic_daozhang` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '到账记录截图',
  `submitted_at` timestamp(0) NULL DEFAULT NULL COMMENT '提交时间',
  `approved_at` timestamp(0) NULL DEFAULT NULL COMMENT '审核通过时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_app_id`(`app_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_rating`(`dingbaojine`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_submitted_at`(`submitted_at`) USING BTREE,
  INDEX `idx_approved_at`(`approved_at`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_view_count`(`view_count`) USING BTREE,
  INDEX `idx_like_count`(`like_count`) USING BTREE,
  INDEX `idx_app_user`(`app_id`, `user_id`) USING BTREE,
  INDEX `idx_status_submitted`(`status`, `submitted_at`) USING BTREE,
  INDEX `idx_status_featured`(`status`, `is_featured`) USING BTREE,
  INDEX `idx_app_status`(`app_id`, `status`) USING BTREE,
  INDEX `idx_user_status`(`user_id`, `status`) USING BTREE,
  INDEX `idx_difficulty_rating`(`tixianmenkan`, `dingbaojine`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评测报告表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_system_logs
-- ----------------------------
DROP TABLE IF EXISTS `zc_system_logs`;
CREATE TABLE `zc_system_logs`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `type` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `add_time` timestamp(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = MyISAM AUTO_INCREMENT = 39 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_user
-- ----------------------------
DROP TABLE IF EXISTS `zc_user`;
CREATE TABLE `zc_user`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `pid` bigint(20) UNSIGNED NOT NULL DEFAULT 0,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `pwd` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '密码',
  `nick_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用户昵称',
  `real_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '真实姓名',
  `avatar_url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '头像URL',
  `gender` tinyint(4) NULL DEFAULT 0 COMMENT '性别',
  `birthday` date NULL DEFAULT NULL COMMENT '生日',
  `province` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '省份',
  `city` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '城市',
  `total_points` int(11) NULL DEFAULT 0 COMMENT '总积分',
  `available_points` int(11) NULL DEFAULT 0 COMMENT '可用积分',
  `level` int(11) NULL DEFAULT 1 COMMENT '用户等级',
  `invite_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '推广码',
  `status` tinyint(3) UNSIGNED NULL DEFAULT 1 COMMENT '用户状态',
  `last_login_at` timestamp(0) NULL DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_phone`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_user_logins
-- ----------------------------
DROP TABLE IF EXISTS `zc_user_logins`;
CREATE TABLE `zc_user_logins`  (
  `login_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '登录记录ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `login_type` enum('password','sms','wechat','qq') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'password' COMMENT '登录方式',
  `login_ip` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登录IP地址',
  `login_device` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登录设备信息',
  `user_agent` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户代理',
  `login_status` enum('success','failed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'success' COMMENT '登录状态',
  `failure_reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '失败原因',
  `session_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '会话ID',
  `login_duration` int(11) NULL DEFAULT NULL COMMENT '登录时长(秒)',
  `logout_at` timestamp(0) NULL DEFAULT NULL COMMENT '登出时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '登录时间',
  PRIMARY KEY (`login_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_login_ip`(`login_ip`) USING BTREE,
  INDEX `idx_login_status`(`login_status`) USING BTREE,
  INDEX `idx_login_type`(`login_type`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_session_id`(`session_id`) USING BTREE,
  INDEX `idx_user_status`(`user_id`, `login_status`) USING BTREE,
  INDEX `idx_user_created`(`user_id`, `created_at`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户登录记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for zc_water_clues
-- ----------------------------
DROP TABLE IF EXISTS `zc_water_clues`;
CREATE TABLE `zc_water_clues`  (
  `clue_id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '线索ID',
  `app_id` bigint(20) UNSIGNED NOT NULL COMMENT '应用ID',
  `user_id` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `clue_title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '线索标题',
  `clue_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '线索内容',
  `clue_type` enum('bug','loophole','promotion','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'other' COMMENT '线索类型',
  `difficulty_level` enum('easy','medium','hard') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'medium' COMMENT '难度等级',
  `expected_reward` decimal(10, 2) NULL DEFAULT NULL COMMENT '预期收益',
  `actual_reward` decimal(10, 2) NULL DEFAULT NULL COMMENT '实际收益',
  `success_rate` decimal(5, 2) NULL DEFAULT NULL COMMENT '成功率(%)',
  `risk_level` enum('low','medium','high') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'medium' COMMENT '风险等级',
  `steps` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '操作步骤',
  `screenshots` json NULL COMMENT '截图列表(JSON格式)',
  `tags` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签',
  `reward_points` int(11) NULL DEFAULT 0 COMMENT '奖励积分',
  `status` enum('draft','submitted','approved','rejected','expired') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'draft' COMMENT '状态',
  `view_count` int(11) NULL DEFAULT 0 COMMENT '查看次数',
  `like_count` int(11) NULL DEFAULT 0 COMMENT '点赞次数',
  `try_count` int(11) NULL DEFAULT 0 COMMENT '尝试次数',
  `success_count` int(11) NULL DEFAULT 0 COMMENT '成功次数',
  `is_featured` tinyint(1) NULL DEFAULT 0 COMMENT '是否推荐',
  `expires_at` timestamp(0) NULL DEFAULT NULL COMMENT '过期时间',
  `submitted_at` timestamp(0) NULL DEFAULT NULL COMMENT '提交时间',
  `approved_at` timestamp(0) NULL DEFAULT NULL COMMENT '审核通过时间',
  `created_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`clue_id`) USING BTREE,
  INDEX `idx_app_id`(`app_id`) USING BTREE,
  INDEX `idx_user_id`(`user_id`) USING BTREE,
  INDEX `idx_clue_type`(`clue_type`) USING BTREE,
  INDEX `idx_difficulty_level`(`difficulty_level`) USING BTREE,
  INDEX `idx_risk_level`(`risk_level`) USING BTREE,
  INDEX `idx_status`(`status`) USING BTREE,
  INDEX `idx_is_featured`(`is_featured`) USING BTREE,
  INDEX `idx_expires_at`(`expires_at`) USING BTREE,
  INDEX `idx_submitted_at`(`submitted_at`) USING BTREE,
  INDEX `idx_approved_at`(`approved_at`) USING BTREE,
  INDEX `idx_created_at`(`created_at`) USING BTREE,
  INDEX `idx_success_rate`(`success_rate`) USING BTREE,
  INDEX `idx_expected_reward`(`expected_reward`) USING BTREE,
  INDEX `idx_app_user`(`app_id`, `user_id`) USING BTREE,
  INDEX `idx_status_submitted`(`status`, `submitted_at`) USING BTREE,
  INDEX `idx_status_featured`(`status`, `is_featured`) USING BTREE,
  INDEX `idx_app_status`(`app_id`, `status`) USING BTREE,
  INDEX `idx_user_status`(`user_id`, `status`) USING BTREE,
  INDEX `idx_type_difficulty`(`clue_type`, `difficulty_level`) USING BTREE,
  INDEX `idx_featured_reward`(`is_featured`, `expected_reward`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '放水线索表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
