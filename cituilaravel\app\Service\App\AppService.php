<?php
declare(strict_types=1);

namespace App\Service\App;

use App\Service\BaseService;
use App\Models\Citui\App;
use App\Models\Citui\AppCategory;

class AppService extends BaseService
{
    /**
     * 获取首页数据
     * @return array
     */
    public function getHomeData(): array
    {
        // 获取推荐的APP列表
        $query = App::query()
            ->where('status', 1)
            ->orderBy('id', 'desc');

        // 获取总数
        $total = $query->count();

        // 如果数据库中没有数据，返回模拟数据
        if ($total == 0) {
            return [
                'list' => [],
                'page' => $this->page_no,
                'page_size' => $this->per_page,
                'total' => $total,
                'has_more' => false
            ];
        }

        // 获取分页数据
        $apps = $query->forPage($this->page_no, $this->per_page)->get();

        $appList = $apps->map(function ($app) {
            return [
                'id' => $app->id,
                'name' => $app->app_name,
                'type' => $this->getAppType($app->category_id),
                'rating' => (float) $app->rating,
                'downloadCount' => $app->download_count,
                'updateTime' => $app->updated_at->format('m-d H:i'),
                'tags' => $this->generateTags($app)
            ];
        })->toArray();

        return [
            'list' => $appList,
            'page' => $this->page_no,
            'page_size' => $this->per_page,
            'total' => $total,
            'has_more' => ($this->page_no * $this->per_page) < $total
        ];
    }

    /**
     * 根据分类ID获取APP类型
     * @param int $categoryId
     * @return string
     */
    private function getAppType(int $categoryId): string
    {
        // 根据分类ID映射到前端需要的类型
        $typeMap = [
            1 => 'coin',    // 金币类
            2 => 'video',   // 视频类
            3 => 'cash',    // 现金类
        ];
        
        return $typeMap[$categoryId] ?? 'coin';
    }

    /**
     * 生成标签数据
     * @param App $app
     * @return array
     */
    private function generateTags(App $app): array
    {
        $tags = [];
        
        // 根据APP特性生成标签
        if ($app->app_label) {
            $labels = explode(',', $app->app_label);
            if ($labels) {
                foreach ($labels as $label) {
                    $tags[] = [
                        'text' => $label,
                        'type' => $this->getTagType($label),
                        'emoji' => $this->getTagEmoji($label)
                    ];
                }
            }
        }
        
        // 如果没有特性标签，生成默认标签
        if (empty($tags)) {
            return [];
        }
        
        return array_slice($tags, 0, 4); // 最多显示4个标签
    }

    /**
     * 获取标签类型
     * @param string $feature
     * @return string
     */
    private function getTagType(string $feature): string
    {
        $typeMap = [
            '热门' => 'red',
            '推荐' => 'red',
            '手动' => 'blue',
            '秒提现' => 'amber',
            '免费试用' => 'green',
            '免费体验' => 'green',
            '新人' => 'green',
            '顶包' => 'blue',
            '简单' => 'red',
            '走路赚钱' => 'amber'
        ];
        
        foreach ($typeMap as $keyword => $type) {
            if (strpos($feature, $keyword) !== false) {
                return $type;
            }
        }
        
        return 'gray';
    }

    /**
     * 获取标签emoji
     * @param string $feature
     * @return string
     */
    private function getTagEmoji(string $feature): string
    {
        $emojiMap = [
            '热门' => '🔥',
            '推荐' => '🔥',
            '秒提现' => '⚡',
            '手动' => '🔥',
            '简单' => '🔥',
            '走路赚钱' => '⚡'
        ];
        
        foreach ($emojiMap as $keyword => $emoji) {
            if (strpos($feature, $keyword) !== false) {
                return $emoji;
            }
        }
        
        return '';
    }

    /**
     * 获取默认标签
     * @param App $app
     * @return array
     */
    private function getDefaultTags(App $app): array
    {
        $defaultTags = [
            ['text' => '热门', 'type' => 'red', 'emoji' => '🔥'],
            ['text' => '秒提现', 'type' => 'amber', 'emoji' => '⚡'],
            ['text' => '免费试用', 'type' => 'green', 'emoji' => '']
        ];
        
        // 根据评分和下载量添加特殊标签
        if ($app->rating >= 4.5) {
            array_unshift($defaultTags, ['text' => '推荐', 'type' => 'red', 'emoji' => '🔥']);
        }
        
        if ($app->download_count >= 1000) {
            $defaultTags[] = ['text' => '￥0.1起提', 'type' => 'gray', 'emoji' => '⚡'];
        }
        
        return $defaultTags;
    }

    /**
     * 获取模拟数据（当数据库中没有数据时使用）
     * @param int $page
     * @param int $pageSize
     * @return array
     */
    private function getMockData(int $page = 1, int $pageSize = 10): array
    {
        // 模拟数据模板，与前端Mock数据结构完全一致
        $mockApps = [
            [
                'id' => 1,
                'name' => '趣接成语',
                'type' => 'coin',
                'rating' => 4.6,
                'downloadCount' => 1260,
                'updateTime' => '05-21 12:01',
                'tags' => [
                    ['text' => '手动', 'type' => 'blue', 'emoji' => '🔥'],
                    ['text' => '￥0.1起提', 'type' => 'gray', 'emoji' => '⚡'],
                    ['text' => '新人0.1', 'type' => 'green', 'emoji' => ''],
                    ['text' => '顶包￥2', 'type' => 'blue', 'emoji' => '']
                ]
            ],
            [
                'id' => 2,
                'name' => '番茄短剧',
                'type' => 'video',
                'rating' => 4.3,
                'downloadCount' => 980,
                'updateTime' => '05-20 18:45',
                'tags' => [
                    ['text' => '推荐', 'type' => 'red', 'emoji' => '🔥'],
                    ['text' => '秒提现', 'type' => 'amber', 'emoji' => '⚡'],
                    ['text' => '免费体验', 'type' => 'green', 'emoji' => '']
                ]
            ],
            [
                'id' => 3,
                'name' => '小小成语库',
                'type' => 'cash',
                'rating' => 4.2,
                'downloadCount' => 760,
                'updateTime' => '05-19 14:30',
                'tags' => [
                    ['text' => '简单', 'type' => 'red', 'emoji' => '🔥'],
                    ['text' => '走路赚钱', 'type' => 'amber', 'emoji' => '⚡'],
                    ['text' => '免费体验', 'type' => 'green', 'emoji' => '']
                ]
            ],
            [
                'id' => 4,
                'name' => '成语大师',
                'type' => 'coin',
                'rating' => 4.5,
                'downloadCount' => 1100,
                'updateTime' => '05-18 16:20',
                'tags' => [
                    ['text' => '热门', 'type' => 'red', 'emoji' => '🔥'],
                    ['text' => '秒提现', 'type' => 'amber', 'emoji' => '⚡'],
                    ['text' => '免费试用', 'type' => 'green', 'emoji' => '']
                ]
            ],
            [
                'id' => 5,
                'name' => '短视频赚钱',
                'type' => 'video',
                'rating' => 4.1,
                'downloadCount' => 850,
                'updateTime' => '05-17 11:35',
                'tags' => [
                    ['text' => '热门', 'type' => 'red', 'emoji' => '🔥'],
                    ['text' => '秒提现', 'type' => 'amber', 'emoji' => '⚡'],
                    ['text' => '免费试用', 'type' => 'green', 'emoji' => '']
                ]
            ],
            [
                'id' => 6,
                'name' => '走路小助手',
                'type' => 'cash',
                'rating' => 4.4,
                'downloadCount' => 920,
                'updateTime' => '05-16 09:45',
                'tags' => [
                    ['text' => '热门', 'type' => 'red', 'emoji' => '🔥'],
                    ['text' => '秒提现', 'type' => 'amber', 'emoji' => '⚡'],
                    ['text' => '免费试用', 'type' => 'green', 'emoji' => '']
                ]
            ],
            [
                'id' => 7,
                'name' => '答题达人',
                'type' => 'coin',
                'rating' => 4.3,
                'downloadCount' => 780,
                'updateTime' => '05-15 13:10',
                'tags' => [
                    ['text' => '热门', 'type' => 'red', 'emoji' => '🔥'],
                    ['text' => '秒提现', 'type' => 'amber', 'emoji' => '⚡'],
                    ['text' => '免费试用', 'type' => 'green', 'emoji' => '']
                ]
            ]
        ];

        // 计算分页
        $total = count($mockApps);
        $offset = ($page - 1) * $pageSize;
        $list = array_slice($mockApps, $offset, $pageSize);

        return [
            'list' => $list,
            'page' => $page,
            'page_size' => $pageSize,
            'total' => $total,
            'has_more' => ($page * $pageSize) < $total
        ];
    }
}
