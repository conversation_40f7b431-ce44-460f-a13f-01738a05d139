<?php
declare(strict_types=1);

namespace App\Models\Citui;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Builder;

class App extends BaseModel
{
    protected $table = 'apps';

    protected $fillable = [
        'category_id',
        'app_name',
        'app_package',
        'app_version',
        'developer',
        'app_label',
        'app_size',
        'download_url',
        'logo_url',
        'description',
        'features',
        'screenshots',
        'rating',
        'rating_count',
        'download_count',
        'view_count',
        'status',
        'is_featured',
        'created_at',
        'updated_at'
    ];

    protected $casts = [
        'features' => 'array',
        'screenshots' => 'array',
        'rating' => 'decimal:2',
        'is_featured' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];
}